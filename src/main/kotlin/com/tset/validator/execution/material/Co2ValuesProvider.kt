package com.tset.validator.execution.material

import aws.smithy.kotlin.runtime.content.BigDecimal
import com.github.doyaaaaaken.kotlincsv.client.CsvReader
import com.nu.bom.core.publicapi.dtos.FieldParameter
import com.tset.validator.execution.RegionBuilder

object Co2ValuesProvider {
    var simulationResults: List<Map<String, String>>? = null

    fun allSimulationResults(): List<Map<String, String>> {
        if(simulationResults == null) {
            val lines =
                CsvReader().readAll(readResourceFile("co2simulation/co2SimpleSimulation.csv")) +
                    CsvReader().readAll(readResourceFile("co2simulation/co2simulation CHILL DCA.csv")) +
                    CsvReader().readAll(readResourceFile("co2simulation/co2simulation PREC.csv")) +
                    CsvReader().readAll(readResourceFile("co2simulation/co2simulation SAND.csv")) +
                    CsvReader().readAll(readResourceFile("co2simulation/co2simulation VPREC.csv")) +
                    CsvReader().readAll(readResourceFile("co2simulation/co2simulation BART.csv")) +
                    CsvReader().readAll(readResourceFile("co2simulation/co2simulation LAST.csv"))

            val headers = lines[0]
            simulationResults = lines.drop(1).map { line ->
                headers.mapIndexed { index, header ->
                    header to line[index]
                }.toMap()
            }
            return simulationResults!!
        } else {
            return simulationResults!!
        }
    }

    fun getEmissionWithEffictivitiesForTsetData(materialName: String, materialType: String): List<EmissionWithEffictivities> {
        return allSimulationResults()
            .asSequence()
            .filter { it["materialName"] == materialName }
            .filter { it["materialType"] == materialType }
            .filter { it["location"] != null  }
            .filter { it["materialFurnaceType"] != null  }
            .filter { it["substancesAndFractions"] != null  }
            .filter { it["co2PerUnit"] != null  }
            .map {
                EmissionWithEffictivities(
                    RegionBuilder.RegionKey(it["location"]!!),
                    it["materialFurnaceType"]!!,
                    it["substancesAndFractions"]!!,
                    it["co2PerUnit"]!!
                )
            }
            .toList()
    }

    fun getEmissionWithEffictivitiesForAccountData(materialType: String, materialFields: List<FieldParameter>,): List<EmissionWithEffictivities> {
        val materialSubstances = materialFields.firstOrNull { it.name == "materialSubstances" }
        return allSimulationResults()
            .asSequence()
            .filter { compareSubstances(it["substancesAndFractions"], materialSubstances) }
            .filter { it["materialType"] == materialType }
            .filter { it["location"] != null  }
            .filter { it["materialFurnaceType"] != null  }
            .filter { it["substancesAndFractions"] != null  }
            .filter { it["co2PerUnit"] != null  }
            .map {
                EmissionWithEffictivities(
                    RegionBuilder.RegionKey(it["location"]!!),
                    it["materialFurnaceType"]!!,
                    it["substancesAndFractions"]!!,
                    it["co2PerUnit"]!!
                )
            }
            .toList()
    }

    private fun compareSubstances(substancesAndFractions: String?, materialSubstances: FieldParameter?): Boolean {
        val targetString = substancesAndFractions ?: ""

        val isMaterialSubstancesEmpty = materialSubstances?.value.let {
            it !is List<*> || it.isEmpty()
        }

        if (targetString.isEmpty() || isMaterialSubstancesEmpty) return false

        val generatedHeader = materialSubstances?.let { generateSubstancesHeader(it) } ?: ""
        return targetString == generatedHeader
    }

    // Example of output: C_0.0017_0.0023|Cr_0.0_0.003|Fe_0.96785_0.9883|Mn_0.01_0.015|Mo_0.0_0.0015|Ni_0.0_0.004|P_0.0_2.0E-4|S_0.0_1.5E-4|Si_0.0_0.006
    fun generateSubstancesHeader(substances: FieldParameter): String {
        val substanceToMinMax = (substances.value as ArrayList<Map<String, Any>>).associateBy { it["substance"] }
        val listOfSubstancesNames = substanceToMinMax.keys.toTypedArray()
        listOfSubstancesNames.sort()
        return listOfSubstancesNames.map {
            val foundRates = substanceToMinMax[it] ?: mapOf("min" to "ERROR_FETCHING_MIN", "max" to "ERROR_FETCHING_MAX")
            "${it}_${foundRates["min"]}_${foundRates["max"]}"
        }.joinToString("|")
    }

    fun getMagnesiumParts(
        materialName: String,
        materialType: String,
        materialFields: List<FieldParameter>
    ): Pair<BigDecimal, BigDecimal> {
        val substances = materialFields.firstOrNull { it.name == "substancesAndFractions" }
        return if (substances != null) {
            (substances.value as List<Substance>).firstOrNull {
                it.name == "Mg"
            }?.let {
                // it value is like this Mg_0.0045_0.0065
                val minValue = it.min
                val maxValue = it.max
                minValue to maxValue
            } ?: (BigDecimal(0) to BigDecimal(0))
        } else {
            println("No substances info for $materialName of type $materialType")
            BigDecimal(0) to BigDecimal(0)
        }
    }

    data class Substance(
        val name: String,
        val min: BigDecimal,
        val max: BigDecimal,
    )

    private fun readResourceFile(filename: String): String =
        object {}.javaClass.classLoader.getResourceAsStream(filename)?.bufferedReader().use {
            it?.readText()
        } ?: ""
}

data class EmissionWithEffictivities(
    val location: RegionBuilder.RegionKey,
    val furnaceType: String,
    val substances: String,
    val co2PerUnit: String,
)