package com.tset.validator.execution.material

import aws.smithy.kotlin.runtime.content.BigDecimal
import com.nu.bom.core.publicapi.dtos.FieldParameter
import com.nu.masterdata.dto.v1.detail.*
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.nu.masterdata.dto.v1.schema.*
import com.tset.validator.execution.MdMigrationMasterDataDetailDto
import com.tset.validator.execution.material.PriceAndEmissionDetails.createPriceAndEmissionDetailForRawMaterials
import jakarta.validation.Valid

object MaterialDetailBuilder {
    private fun getField(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        fieldName: String,
    ): FieldParameter? = nbkMaterial.fields.firstOrNull { it.name == fieldName }

    private fun createRawMaterial(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        masterDataType: MaterialMasterDataType,
        classificationFieldNames: List<String>,
        hasCo2Value: Boolean = false,
    ): Pair<HeaderDto, List<DetailDto>>? {
        println("migrating ${nbkMaterial.key} of type ${nbkMaterial.type}")

        var dimensionField = getField(nbkMaterial, "dimension")

        if (dimensionField == null || dimensionField.value == null) {
            println("NO_DIMENSTION for material ${nbkMaterial.key} of type ${nbkMaterial.type}")
            dimensionField = FieldParameter(
                name = "dimension",
                value = "MASS",
                type = "Dimension",
                unit = "KILOGRAM",
                source = "M"
            )
        }
        val pricePerUnit = getField(nbkMaterial, "pricePerUnit")
        val basePriceValue = if(pricePerUnit?.value == null) {
            getField(nbkMaterial, "materialBasePrice")
        } else {
            pricePerUnit
        }

        val co2Value = getField(nbkMaterial, "cO2PerUnit")

        val designationField = getField(nbkMaterial, "designation")
        val technologyField = getField(nbkMaterial, "technology")

        val displayDesignationField = getField(nbkMaterial, "displayDesignation")
        val masterdataBaseCurrencyField = getField(nbkMaterial, "masterdataBaseCurrency") ?:
            FieldParameter(
                name = "masterdataBaseCurrency",
                value = "EUR",
                type = "Currency",
                unit = null,
                source = "M"
            ).also { println("NO masterdataBaseCurrency for material ${nbkMaterial.key} of type ${nbkMaterial.type}") }

        val baseCurrencyField = getField(nbkMaterial, "baseCurrency") ?:
            masterdataBaseCurrencyField.also { println("NO baseCurrency for material ${nbkMaterial.key} of type ${nbkMaterial.type}") }

        // val itemNumberField = getField(nbkMaterial, "itemNumber")
        var costUnitField = getField(nbkMaterial, "costUnit")
        if (costUnitField?.value == null) {
            println("NO CostUnit for material ${nbkMaterial.key} of type ${nbkMaterial.type}")
            costUnitField = FieldParameter(
                name = "costUnit",
                value = "KILOGRAM",
                type = "Text",
                unit = null,
                source = "M"
            )
        }
        val classificationFields = (classificationFieldNames + "itemNumber").mapNotNull { getField(nbkMaterial, it)  }

        val header =
            createHeader(
                masterDataType,
                nbkMaterial.key,
                nbkMaterial.type,
                displayDesignationField,
                designationField,
                technologyField,
                baseCurrencyField,
                costUnitField,
                classificationFields,
            )

        val emissionWithEffictivities =
            if(nbkMaterial.representsAccountData) {
                Co2ValuesProvider.getEmissionWithEffictivitiesForAccountData(nbkMaterial.type, nbkMaterial.fields)
            } else {
                Co2ValuesProvider.getEmissionWithEffictivitiesForTsetData(nbkMaterial.key, nbkMaterial.type)
            }

        val details =
            createPriceAndEmissionDetailForRawMaterials(
                headerKey = (header?.key as SimpleKeyDto?)?.key ?: "unknown",
                basePriceValue = basePriceValue,
                sumOfPriceComponentsValue = null,
                currency = masterdataBaseCurrencyField,
                denominatorUnit = costUnitField,
                dimension = dimensionField,
                representsAccountData = nbkMaterial.representsAccountData,
                emissionWithEffictivities = emissionWithEffictivities,
                hasCo2Value = hasCo2Value,
                co2Value = co2Value,
            )
        return if (header == null) null else header to details
    }

    fun createRawMaterialCoil(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialCoil: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? =
        createRawMaterial(
            nbkMaterial,
            rawMaterialCoil,
            listOf(
                "materialGroup",
                "density",
                "strainHardeningExponent",
                "anisotropyCoefficient",
                "stressStrainPowerCurveCoefficient",
                "ultimateTensileStrength",
                "youngModulus",
                "poissonRatio",
                "yieldStress",
            )
        )

    fun createRawMaterialCoatingPCBA(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        masterDataType: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? =
        createRawMaterial(
            nbkMaterial,
            masterDataType,
            listOf()
        )

    fun createRawMaterialManual(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        masterDataType: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? =
        createRawMaterial(
            nbkMaterial,
            masterDataType,
            listOf(),
            true,
        )

    fun createRawMaterialMetallicCoating(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialMetallicCoating: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? = createRawMaterial(
        nbkMaterial,
        rawMaterialMetallicCoating,
        listOf(
            "equivalentWeight",
            "density"
        )
    )

    fun createRawMaterialPaint(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialPaint: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? = createRawMaterial(
        nbkMaterial,
        rawMaterialPaint,
        listOf(
            "paintCoatType",
            "thinnerRate",
            "hardenerRate",
            "flashOffTime",
            "spreadingRatePerMicrometerWet",
            "density",
        )
    )

    fun createRawMaterialRareEarth(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialRareEarth: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? = createRawMaterial(
        nbkMaterial,
        rawMaterialRareEarth,
        listOf(
            "density",
        )
    )

    fun createRawMaterialWax(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialWax: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? = createRawMaterial(
        nbkMaterial,
        rawMaterialWax,
        listOf(
            "moldTemperature",
            "injectingTemperature",
            "moldSeparationTemperature",
            "thermalDiffusivity",
            "density",
        )
    )

    fun createRawMaterialPowder(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialPowder: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? = createRawMaterial(
        nbkMaterial,
        rawMaterialPowder,
        listOf(
            "density",
        )
    )

    fun createRawMaterialBar(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialBar: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? = createRawMaterial(
        nbkMaterial,
        rawMaterialBar,
        listOf(
            "materialGroup",
            "quenchingPossible",
            "caseHardeningPossible",
            "materialGroupQuenched",
            "preheated",
            "temperatureDifferenceForging",
            "solutionAnnealingTime",
            "ageingTime",
            "temperingSoakingTime",
            "soakingTime",
            "density",
            "specificThermalCapacity",
            "surfaceFinish",
            "barLength",
            "barProcess",
        )
    )

    fun createRawMaterialWireRod(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialWireRod: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? = createRawMaterial(
        nbkMaterial,
        rawMaterialWireRod,
        listOf(
            "materialGroup",
            "materialGroupQuenched",
            "density",
            "specificThermalCapacity",
            "preheated",
        )
    )

    fun createRawMaterialPipe(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialPipe: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? = createRawMaterial(
        nbkMaterial,
        rawMaterialPipe,
        listOf(
            "materialGroup",
            "materialGroupQuenched",
            "density",
            "specificThermalCapacity",
        )
    )

    fun createRawMaterialSand(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialSand: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? = createRawMaterial(
        nbkMaterial,
        rawMaterialSand,
        listOf(
            "density",
        )
    )

    fun createRawMaterialRubber(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialRubber: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? = createRawMaterial(
        nbkMaterial,
        rawMaterialRubber,
        listOf(
            "rubberType",
            "moldTemperature",
            "internalMoldPressure",
            "heatingTimeFactor",
            "specificThermalCapacity",
            "density",
        )
    )

    fun createRawMaterialPlasticGranulate(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialPlasticGranulate: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? = createRawMaterial(
        nbkMaterial,
        rawMaterialPlasticGranulate,
        listOf(
            "fillerMaterial",
            "fillerRatio",
            "minMoldTemperature",
            "maxMoldTemperature",
            "moldTemperature",
            "injectingTemperature",
            "moldSeparationTemperature",
            "averageInjectionVelocity",
            "materialFactor",
            "shrinkageBehavior",
            "thermalDiffusivity",
            "density",
            "minInternalMoldPressure",
            "maxInternalMoldPressure",
        )
    )

    fun createRawMaterialLamella(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialLamella: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? {
        var baseCurrencyField = getField(nbkMaterial, "baseCurrency")
        var dimensionField = getField(nbkMaterial, "dimension")
        if (dimensionField == null || dimensionField.value == null) {
            println("NO_DIMENSTION for material ${nbkMaterial.key} of type ${nbkMaterial.type}. Defaulting to MASS")
            dimensionField = FieldParameter(
                name = "dimension",
                value = "MASS",
                type = "Dimension",
                unit = "KILOGRAM",
                source = "M"
            )
        }

        val itemNumberField = getField(nbkMaterial, "itemNumber")
        val designationField = getField(nbkMaterial, "designation")
        val technologyField = getField(nbkMaterial, "technology")
        val lamellaThicknessField = getField(nbkMaterial, "lamellaThickness")
        val densityField = getField(nbkMaterial, "density")
        val stackingFactorField = getField(nbkMaterial, "stackingFactor")
        var basePriceField = getField(nbkMaterial, "basePrice")
        if(basePriceField == null) {
            println("NO basePrice for material ${nbkMaterial.key} of type ${nbkMaterial.type}. Defaulting to materialBasePrice")
            basePriceField = getField(nbkMaterial, "materialBasePrice")
        }
        val gluePriceField = getField(nbkMaterial, "gluePrice")
        val displayDesignationField = getField(nbkMaterial, "displayDesignation")
        var masterdataBaseCurrencyField = getField(nbkMaterial, "masterdataBaseCurrency")
        if(masterdataBaseCurrencyField == null) {
            println("NO masterdataBaseCurrency for material ${nbkMaterial.key} of type ${nbkMaterial.type}. Defaulting to EUR")
            masterdataBaseCurrencyField = FieldParameter(
                name = "masterdataBaseCurrency",
                value = "EUR",
                type = "Currency",
                unit = null,
                source = "M"
            )
        }
        if(baseCurrencyField == null) {
            println("NO baseCurrencyField for material ${nbkMaterial.key} of type ${nbkMaterial.type}. Defaulting to masterdataBaseCurrency")
            baseCurrencyField = masterdataBaseCurrencyField
        }

        var costUnitField = getField(nbkMaterial, "costUnit")
        if (costUnitField == null || costUnitField.value == null) {
            println("NO CostUnit for material ${nbkMaterial.key} of type ${nbkMaterial.type}. Defaulting to KILOGRAM")
            costUnitField = FieldParameter(
                name = "costUnit",
                value = "KILOGRAM",
                type = "Text",
                unit = null,
                source = "M"
            )
        }
        val classificationFields =
            listOfNotNull(
                lamellaThicknessField,
                densityField,
                stackingFactorField,
                gluePriceField,
                itemNumberField,
            )

        val header =
            createHeader(
                rawMaterialLamella,
                nbkMaterial.key,
                nbkMaterial.type,
                displayDesignationField,
                designationField,
                technologyField,
                baseCurrencyField,
                costUnitField,
                classificationFields,
            )
        val emissionWithEffictivities =
            if(nbkMaterial.representsAccountData) {
                Co2ValuesProvider.getEmissionWithEffictivitiesForAccountData(nbkMaterial.type, nbkMaterial.fields)
            } else {
                Co2ValuesProvider.getEmissionWithEffictivitiesForTsetData(nbkMaterial.key, nbkMaterial.type)
            }

        val createdDetails =
            createPriceAndEmissionDetailForRawMaterials(
                headerKey = (header?.key as SimpleKeyDto?)?.key ?: "unknown",
                basePriceValue = basePriceField,
                sumOfPriceComponentsValue = null,
                emissionWithEffictivities = emissionWithEffictivities,
                currency = masterdataBaseCurrencyField,
                denominatorUnit = costUnitField,
                dimension = dimensionField,
                representsAccountData = nbkMaterial.representsAccountData,
                hasCo2Value = false,
                co2Value = null,
            )

        return if (header == null) null else header to createdDetails
    }

    fun createRawMaterialSheet(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialSheet: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? = createRawMaterial(
        nbkMaterial,
        rawMaterialSheet,
        listOf(
            "materialGroup",
            "rollingType",
            "tensileStrength",
            "density",
            "elongationAtBreak",
        )
    )

    fun createRawMaterialIngot(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialIngot: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? =
        createRawMaterial(
            nbkMaterial,
            rawMaterialIngot,
            listOf(
                "caseHardeningPossible",
                "preheated",
                "density",
                "specificThermalCapacity",
                "solutionAnnealingTime",
                "ageingTime",
                "temperingSoakingTime",
                "soakingTime",
            )
        )

    fun createRawMaterialCastingAlloy(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialCastingAlloy: MaterialMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? {
        val technologyClassificationKeys = MaterialMapper.getTechnologyClassification(getField(nbkMaterial, "technology"))

        val nbkMaterialWithMagnesium = if(technologyClassificationKeys.contains("tset.ref.classification.DCA")) {
            val (min, max) = Co2ValuesProvider.getMagnesiumParts(nbkMaterial.key, nbkMaterial.type, nbkMaterial.fields)
            val hasMagnesium = if(min > BigDecimal(0.6) || max > BigDecimal(0.6)) {
                "true"
            } else {
                "false"
            }

            nbkMaterial.copy(
                fields = nbkMaterial.fields.plus(
                    FieldParameter(
                        name = "hasMagnesium",
                        value = hasMagnesium,
                        type = "Boolean",
                        unit = null,
                        source = "M"
                    )
                )
            )
        } else {
            nbkMaterial
        }

        return createRawMaterial(
            nbkMaterialWithMagnesium,
            rawMaterialCastingAlloy,
            listOf(
                "materialGroup",
                "quenchingPossible",
                "caseHardeningPossible",
                "materialGroupQuenched",
                "density",
                "minCastingTemperature",
                "maxCastingTemperature",
                "liquidusTemperature",
                "specificThermalCapacity",
                "minChillCastingTemperature",
                "maxChillCastingTemperature",
                "soakingTime",
                "temperingSoakingTime",
                "solutionAnnealingTime",
                "ageingTime",
                "hasMagnesium"
            )
        )
    }


    fun createRawMaterialPaperCoil(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialRubber: MaterialMasterDataType
    ): Pair<HeaderDto, List<DetailDto>>? =
        createRawMaterial(
            nbkMaterial,
            rawMaterialRubber,
            listOf(
                "paperQuality",
                "paperCategoryPaperCoil",
                "density"
            )
        )

    fun createRawMaterialPaperSheet(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialRubber: MaterialMasterDataType
    ): Pair<HeaderDto, List<DetailDto>>? =
        createRawMaterial(
            nbkMaterial,
            rawMaterialRubber,
            listOf(
                "paperQuality",
                "paperCategoryPaperSheet",
                "density"
            )
        )

    fun createRawMaterialBase(
        nbkMaterial: MdMigrationMasterDataDetailDto,
        rawMaterialRubber: MaterialMasterDataType
    ): Pair<HeaderDto, List<DetailDto>>? =
        createRawMaterial(
            nbkMaterial,
            rawMaterialRubber,
            listOf(
                "density"
            )
        )

    const val MATERIAL_CLASSIFICATION_TYPE = "tset.ref.classification-type.material"
    private fun createHeader(
        masterDataType: MaterialMasterDataType,
        originalKey: String,
        originalType: String?,
        displayDesignationField: FieldParameter?,
        designationField: FieldParameter?,
        technologyField: FieldParameter?,
        baseCurrencyField: FieldParameter?,
        costUnitField: FieldParameter?,
        fields: List<FieldParameter>,
    ): HeaderDto? {
        val key = originalType?.let { "$originalKey-$originalType" } ?: originalKey
        val materialClassificationTypeKey = MATERIAL_CLASSIFICATION_TYPE
        val technologyClassificationTypeKey = "tset.ref.classification-type.technology"
        val materialClassKey = MaterialMapper.getMaterialClassification(masterDataType.name)
        if (materialClassKey == null) {
            println("Cannot create material $key because could not find material classification for master data type $masterDataType.")
            return null
        }
        val technologyClassificationKeys = MaterialMapper.getTechnologyClassification(technologyField)

        val classifications =
            mapOf(
                SimpleKeyDto(materialClassificationTypeKey) to listOf(SimpleKeyDto(materialClassKey)),
                SimpleKeyDto(technologyClassificationTypeKey) to technologyClassificationKeys.map { SimpleKeyDto(it) },
            )

        // val classificationFieldValues = emptyMap<SimpleKeyDto,FieldValueDto>() // createClassificationFieldValues(fields, masterDataType)
        val classificationFieldValues = createClassificationFieldValues(fields, key, masterDataType)

        val currencyIsoCode = baseCurrencyField?.value?.toString()
        if (currencyIsoCode == null) {
            println("Cannot create material $key because no currency is available.")
            return null
        }

        val denominatorUnit = costUnitField?.value?.toString()
        if (denominatorUnit == null) {
            println("Cannot create material $key because no currency is available.")
            return null
        }
        val mdDenominatorUnit = MaterialMapper.getMasterDataUnit(denominatorUnit, "")
        requireNotNull(mdDenominatorUnit) { "header denominator unit was null for nbk $denominatorUnit" }

        val detailValueSchema =
            ValueTypeDetailValueSchemaDto(
                detailValueTypeMapping =
                    mapOf(
                        SimpleKeyDto("price") to
                                DetailValueTypeDto(
                                    key = SimpleKeyDto("price"),
                                    name = "Price",
                                    index = 0,
                                    detailValueSchema =
                                        NumericFieldSchemaDto(
                                            unitOfMeasurement =
                                                UnitOfMeasurementTypeDto(
                                                    numerator = AnyCurrencyTypeDto(SimpleKeyDto("EUR")),
                                                    denominator = UnitTypeDto(SimpleKeyDto(mdDenominatorUnit.unitKey)),
                                                ),
                                        ),
                                ),
                        SimpleKeyDto("emission") to
                                DetailValueTypeDto(
                                    key = SimpleKeyDto("emission"),
                                    name = "Emission",
                                    index = 1,
                                    detailValueSchema =
                                        NumericFieldSchemaDto(
                                            unitOfMeasurement =
                                                UnitOfMeasurementTypeDto(
                                                    numerator = UnitTypeDto(SimpleKeyDto("tset.unit.emission.kilogram_co2e")),
                                                    denominator = UnitTypeDto(SimpleKeyDto(mdDenominatorUnit.unitKey)),
                                                ),
                                        ),
                                ),
                    ),
            )

        return HeaderDto(
            key = SimpleKeyDto(key),
            name = displayDesignationField?.value?.toString() ?: designationField?.value?.toString() ?: key,
            headerTypeKey = SimpleKeyDto(MATERIAL_HEADER_TYPE),
            active = true,
            detailValueSchema = detailValueSchema,
            classifications = classifications,
            classificationFieldValues = classificationFieldValues,
        )
    }

    private fun createClassificationFieldValues(
        fields: List<FieldParameter>,
        key: String,
        masterDataType: MaterialMasterDataType,
    ): Map<@Valid SimpleKeyDto, @Valid FieldValueDto> =
        fields
            .mapNotNull { nbkField ->
                println("-----mapping field: ${nbkField.name},${nbkField.type},${nbkField.unit}")

                val fieldInfo = MaterialMapper.getMaterialClassificationFieldInfo(nbkField.name, masterDataType)
                val valueDto =
                    if(fieldInfo.name == "itemNumber") {
                        nbkField.value?.let { v ->
                            if (v != "") {
                                TextValueDto(
                                    value = v.toString()
                                )
                            } else { null }
                        }
                    } else if (fieldInfo.isNumeric) {
                        if(nbkField.type == "Money"){
                            // money fields are all in EUR
                            // this feature is used to save the glue price in a classification field
                            nbkField.value?.let { v ->
                                NumericValueDto(
                                    value = v as Double,
                                    numerator = CurrencyMeasurementDto(SimpleKeyDto("EUR")),
                                )
                            }
                        } else {
                            val nbkUnit = nbkField.unit
                            val mdUnit = MaterialMapper.getMasterDataUnit(nbkUnit, nbkField.type)
                            if ("Rate" == nbkField.type) {
                                nbkField.value?.let { v ->
                                    NumericValueDto(
                                        value = v as Double,
                                        numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.rate.percentage")),
                                    )
                                }
                            } else if ("Num" == nbkField.type) {
                                nbkField.value?.let { v ->
                                    NumericValueDto(
                                        value = v as Double,
                                        numerator = null,
                                    )
                                }
                            } else if (fieldInfo.csvUnitKey == "Time_HOURS" &&
                                nbkUnit == "SECOND" &&
                                mdUnit?.unitKey == "tset.unit.time.second"
                            ) {
                                nbkField.value?.let { v ->
                                    NumericValueDto(
                                        value = (v as Double) / 3600,
                                        numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.time.hour")),
                                    )
                                }
                            } else if (fieldInfo.csvUnitKey == "Time_MINUTE" &&
                                nbkUnit == "SECOND" &&
                                mdUnit?.unitKey == "tset.unit.time.second"
                            ) {
                                nbkField.value?.let { v ->
                                    NumericValueDto(
                                        value = (v as Double) / 60,
                                        numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.time.minute")),
                                    )
                                }
                            } else {
                                requireNotNull(mdUnit) { "field ${nbkField.name} has no md unit for nbk $nbkUnit" }
                                nbkField.value?.let { v ->
                                    NumericValueDto(
                                        value = v as Double,
                                        numerator = UnitMeasurementDto(SimpleKeyDto(mdUnit.unitKey)),
                                    )
                                }
                            }
                        }

                    } else {
                        nbkField.value?.let { v ->
                            requireNotNull(fieldInfo.lovTypeKey) { "no lov type key provided for field ${nbkField.name}" }

                            // println("---------------$v-----------------------------")
                            val lovEntryKey = MaterialMapper.getLovEntryKey(key, fieldInfo.lovTypeKey, v.toString())
                            LovValueDto(
                                value = SimpleKeyDto(lovEntryKey),
                            )
                        }
                    }
                valueDto?.let {
                    SimpleKeyDto(fieldInfo.mdKey) to valueDto
                }
            }.toMap()
}