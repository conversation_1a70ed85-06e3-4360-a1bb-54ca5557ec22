package com.tset.validator.execution.machine

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonMapperBuilder
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.nu.masterdata.dto.v1.detail.*
import com.nu.masterdata.dto.v1.detail.table.BuiltinLovFilterDto
import com.nu.masterdata.dto.v1.detail.table.DetailQueryDto
import com.nu.masterdata.dto.v1.detail.table.DetailQueryResponseDto
import com.nu.masterdata.dto.v1.detail.table.HeaderAndDetailDto
import com.nu.masterdata.dto.v1.header.HeaderBulkResponseDto
import com.nu.masterdata.dto.v1.header.HeaderBulkResponseErrorDto
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.tset.validator.AccountReference
import com.tset.validator.MasterdataMigrationResult
import com.tset.validator.config.MasterdataMigrationConfig
import com.tset.validator.config.MasterdataType
import com.tset.validator.execution.*
import com.tset.validator.timing.TimingData
import com.tset.validator.timing.TimingType
import com.tset.validator.utils.DurationSerializable
import com.tset.validator.utils.serializable
import com.tset.validator.webclient.ErrorResponse
import com.tset.validator.webclient.HttpMethod
import com.tset.validator.webclient.PublicApiService
import com.tset.validator.webclient.ServiceType
import kotlin.time.Duration

// TODO do we have different types? And do those types have different fields, like materials did?
enum class MachineMasterDataType {
    MACHINE
}

const val MACHINE_HEADER_TYPE = "tset.ref.header-type.machine"
const val DETAIL_VALUE_INVEST_BASE_KEY = "investBase"
const val DETAIL_VALUE_INVEST_FUNDAMENT_KEY = "investFundament"
const val DETAIL_VALUE_INVEST_MISC_KEY = "investMisc"
const val DETAIL_VALUE_INVEST_SETUP_KEY = "investSetup"
const val DETAIL_VALUE_EMISSION_KEY = "emission"

const val ONLY_ACCOUNT_DATA = true
const val READ_DATA_AND_WRITE_TO_FILE = false
const val READ_DATA_FROM_FILE_AND_POST = false

class MasterdataMachineMigration(
    private val client: PublicApiService,
    private val config: MasterdataMigrationConfig,
) : MasterdataMigration {
    private val objectMapper: ObjectMapper

    init {
        require(config.masterdataType == MasterdataType.MACHINES) {
            "invalid masterdata migration type ${config.masterdataType}, expected `${MasterdataType.MACHINES}`"
        }
        objectMapper =
            jacksonMapperBuilder()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .findAndAddModules()
                .build()
    }

    override suspend fun executeMigrationForAccount(accountReference: AccountReference): MasterdataMigrationResult =
        try {
            doWork(accountReference)
        } catch (ex: MasterdataMigrationError) {
            println("error during masterdata migration of account ${accountReference.accountName}: $ex")
            MasterdataMigrationResult(
                TimingData(TimingType.Error, DurationSerializable.fromDuration(Duration.ZERO)),
                accountReference,
                listOf(ErrorResponse.ParseError(ex.message ?: "MasterdataMigrationError", "")),
            )
        }

    private suspend fun doWork(accountReference: AccountReference): MasterdataMigrationResult {
        val accountName = accountReference.accountName
        println("migrate machines of account $accountName to Masterdata")

        val queryDto =
            DetailQueryDto(
                filters =
                    mapOf(
                        SimpleKeyDto("_BUILTIN_modifier") to
                            listOf(
                                BuiltinLovFilterDto(
                                    equals = SimpleKeyDto("tsetLegacyMigrator"),
                                ),
                            ),
                    ),
                showStateOf = null,
                showInactive = false,
                sortOrder = null,
            )

        val strRespNbk =
            MachineMasterDataType.entries.map { entry ->
                entry to
                    client.callAnyEndpoint(
                        accountName,
                        HttpMethod.GET,
                        "api/masterdata/md-migration/type/$entry/account-entities?searchMode=ACCOUNT_ONLY",
                        false,
                        ServiceType.NuBomKotlin,
                    )
            }

        val strRespNbkGlobal =
            MachineMasterDataType.entries.map { entry ->
                entry to
                    client.callAnyEndpoint(
                        accountName,
                        HttpMethod.GET,
                        "api/masterdata/md-migration/type/$entry/account-entities?searchMode=GLOBAL_ONLY",
                        false,
                        ServiceType.NuBomKotlin,
                    )
            }

        val strResponseMd =
            client.callAnyEndpoint(
                accountName,
                HttpMethod.POST,
                "api/md/v1/headertypes/$MACHINE_HEADER_TYPE/details/search?size=200000",
                false,
                ServiceType.MasterData,
                body = objectMapper.writeValueAsString(queryDto),
                headers = getMdRequestHeaders(accountName),
            )

        strRespNbk.forEach {
            if (!it.second.res().ok()) {
                println("error fetching account data from NBK for master data type ${it.first} ${it.second.res().unwrapError()}")
            }
        }

        strRespNbkGlobal.forEach {
            if (!it.second.res().ok()) {
                println("error fetching tset data from NBK for master data type ${it.first} ${it.second.res().unwrapError()}")
            }
        }

        if (!strResponseMd.res().ok()) {
            println("error fetching data from MD ${strResponseMd.res().unwrapError()}")
        }

        val fetchErrors =
            (
                strRespNbk.map { it.second } +
                    strRespNbkGlobal.map { it.second } +
                    strResponseMd
            ).mapNotNull {
                if (!it.res().ok()) {
                    it.res().error
                } else {
                    null
                }
            }

        val headersAndDetailsToPost =
            if (fetchErrors.isEmpty()) {
                val nbkMdDetails: List<MdMigrationMasterDataDetailDto> =
                    strRespNbk
                        .map { resp ->
                            objectMapper.readValue<List<MdMigrationMasterDataDetailDto>?>(
                                resp.second
                                    .res()
                                    .unwrapSuccess(),
                                jacksonTypeRef(),
                            )
                        }.flatten()

                val nbkMdDetailsGlobal: List<MdMigrationMasterDataDetailDto> =
                    strRespNbkGlobal
                        .map { resp ->
                            objectMapper
                                .readValue<List<MdMigrationMasterDataDetailDto>?>(
                                    resp.second
                                        .res()
                                        .unwrapSuccess(),
                                    jacksonTypeRef(),
                                ).map { it.copy(representsAccountData = false) }
                        }.flatten()

                val allNbkDetails =
                    if (ONLY_ACCOUNT_DATA) {
                        nbkMdDetails
                    } else {
                        nbkMdDetailsGlobal + nbkMdDetails
                    }

                if (READ_DATA_AND_WRITE_TO_FILE) {
                    // create json data file
                    val dataAsString = objectMapper.writeValueAsString(allNbkDetails)
                    java.io.File("ref-machine.json").writeText(dataAsString)
                    // stop here and switch to target branch
                    return MasterdataMigrationResult(
                        TimingData(TimingType.Fetch, Duration.INFINITE.serializable()),
                        accountReference,
                        emptyList(),
                    )
                }

                val allNbkDetailsToMigrate =
                    if (READ_DATA_FROM_FILE_AND_POST) {
                        // read json data file
                        val dataAsString = java.io.File("ref-machine.json").readText()
                        objectMapper.readValue<List<MdMigrationMasterDataDetailDto>?>(
                            dataAsString,
                            jacksonTypeRef(),
                        )
                    } else {
                        allNbkDetails
                    }

                // Get latest machines by key and type
                val latestNbkDetails =
                    allNbkDetailsToMigrate
                        .groupBy { it.key to it.type }
                        .mapValues { (_, group) -> group.maxBy { it.year } }
                        .values
                        .toList()

                // TODO: Create classification fields for machines
                // ClassificationBuilder.createMachineClassifications(...)

                val existingMd: DetailQueryResponseDto =
                    objectMapper.readValue(
                        strResponseMd.res().unwrapSuccess(),
                        jacksonTypeRef(),
                    )

                createHeaderAndDetailsToPost(latestNbkDetails, existingMd.content)
            } else {
                null
            }

        val postErrors =
            if (headersAndDetailsToPost != null) {
                val (headers, details) = headersAndDetailsToPost
                println("posting ${headers.size} headers and ${details.size} details")

                val headerPostResult =
                    if (headers.isNotEmpty()) {
                        client.callAnyEndpoint(
                            accountName,
                            HttpMethod.POST,
                            "api/md/v1/headers/bulk",
                            false,
                            ServiceType.MasterData,
                            body = objectMapper.writeValueAsString(headers),
                            headers = getMdRequestHeaders(accountName),
                        )
                    } else {
                        null
                    }

                val detailPostResult =
                    if (details.isNotEmpty()) {
                        client.callAnyEndpoint(
                            accountName,
                            HttpMethod.POST,
                            "api/md/v1/details/bulk",
                            false,
                            ServiceType.MasterData,
                            body = objectMapper.writeValueAsString(details),
                            headers = getMdRequestHeaders(accountName),
                        )
                    } else {
                        null
                    }

                val headerPostError =
                    if (headerPostResult?.res()?.ok() == false) {
                        val bulkErrors: List<HeaderBulkResponseErrorDto> =
                            objectMapper
                                .readValue<List<HeaderBulkResponseDto>?>(
                                    (headerPostResult.res().unwrapError() as ErrorResponse.UnknownErrorResponse).content,
                                    jacksonTypeRef(),
                                ).filterIsInstance<HeaderBulkResponseErrorDto>()
                        val bulkErrorsAsString = objectMapper.writeValueAsString(bulkErrors)
                        println("error putting data into MD $bulkErrorsAsString")
                        headerPostResult.res().unwrapError()
                    } else {
                        null
                    }

                val detailPostError =
                    if (detailPostResult?.res()?.ok() == false) {
                        println("error posting details to MD ${detailPostResult.res().unwrapError()}")
                        detailPostResult.res().unwrapError()
                    } else {
                        null
                    }

                listOfNotNull(headerPostError, detailPostError)
            } else {
                emptyList()
            }

        val allErrors = fetchErrors + postErrors

        val timingType =
            if (allErrors.isEmpty()) {
                TimingType.Unspecified
            } else {
                TimingType.Error
            }
        return MasterdataMigrationResult(
            TimingData(timingType, Duration.INFINITE.serializable()),
            accountReference,
            allErrors,
        )
    }

    private fun getMdRequestHeaders(accountName: String): Map<String, String> =
        mapOf(
            HEADER_USER to MIGRATION_USERNAME,
            HEADER_ACCOUNT to accountName,
            "Content-Type" to "application/json",
        )

    private fun createHeaderAndDetailsToPost(
        nbkMdDetails: List<MdMigrationMasterDataDetailDto>,
        existingMd: List<HeaderAndDetailDto>,
    ): Pair<List<HeaderDto>, List<DetailDto>> {
        println("entries in nbk: ${nbkMdDetails.size}, entries in md: ${existingMd.size}")
        if (nbkMdDetails.isEmpty()) {
            return emptyList<HeaderDto>() to emptyList<DetailDto>()
        }

        val newMachines =
            nbkMdDetails
                .mapNotNull { nbkMachine ->
                    // TODO if there's only one type of Machine, we can remove this when statement.
                    when (MachineMasterDataType.valueOf(nbkMachine.type)) {
                        MachineMasterDataType.MACHINE ->
                            MachineDetailBuilder.createMachine(
                                nbkMachine,
                                MachineMasterDataType.MACHINE,
                            )
                    }
                }

        val headers = newMachines.map { it.first }
        val details =
            newMachines
                .map { it.second }
                .flatten()
                // TODO: Add filtering logic similar to material migration
                // .filter { shouldUpdateDetailInMd(it, existingMd.mapNotNull { hd -> hd.detailDto }) }

        return headers to details
    }
}
