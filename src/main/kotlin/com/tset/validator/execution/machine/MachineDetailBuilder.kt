package com.tset.validator.execution.machine

import com.nu.bom.core.publicapi.dtos.FieldParameter
import com.nu.masterdata.dto.v1.detail.*
import com.nu.masterdata.dto.v1.header.HeaderDto
import com.nu.masterdata.dto.v1.keys.SimpleKeyDto
import com.nu.masterdata.dto.v1.schema.*
import com.tset.validator.execution.MdMigrationMasterDataDetailDto
import com.tset.validator.execution.generic.UnitMapper
import com.tset.validator.execution.material.MaterialEffectivityBuilder
import jakarta.validation.Valid

object MachineDetailBuilder {
    private fun findFieldByName(
        nbkMachine: MdMigrationMasterDataDetailDto,
        fieldName: String,
    ): FieldParameter? = findFieldByName(nbkMachine.fields, fieldName)

    private fun findFieldByName(
        fields: List<FieldParameter>,
        fieldName: String,
    ): FieldParameter? = fields.find { it.name == fieldName }

    private fun createNumericFieldValue(
        fieldValue: Any?,
        numeratorUnitKey: String,
        denominatorUnitKey: String? = null,
        valueConverter: (Double) -> Double = { it }
    ): NumericValueDto {
        val rawValue = (fieldValue as? Number)?.toDouble() ?: 0.0
        val convertedValue = valueConverter(rawValue)

        return NumericValueDto(
            value = convertedValue,
            numerator = UnitMeasurementDto(SimpleKeyDto(numeratorUnitKey)),
            denominator = denominatorUnitKey?.let { UnitMeasurementDto(SimpleKeyDto(it)) }
        )
    }

    private fun createFieldValueWithUnitConversion(nbkField: FieldParameter): FieldValueDto? {
        return when (nbkField.type) {
            "Money" -> {
                // Following logic of MaterialDetailBuilder to convert all Money fields to EUR
                nbkField.value?.let { v ->
                    NumericValueDto(
                        value = v as Double,
                        numerator = CurrencyMeasurementDto(SimpleKeyDto("EUR")),
                    )
                }
            }
            "Rate" -> {
                // All Rate fields should be converted to percentage
                nbkField.value?.let { v ->
                    NumericValueDto(
                        value = v as Double,
                        numerator = UnitMeasurementDto(SimpleKeyDto("tset.unit.rate.percentage")),
                    )
                }
            }
            "Num" -> {
                // Num fields have no units
                nbkField.value?.let { v ->
                    NumericValueDto(
                        value = v as Double,
                        numerator = null,
                    )
                }
            }
            // TODO what about those csvUnitKey things from MaterialDetailBuilder?
            else -> {
                // Use UnitMapper for standard unit conversions
                val nbkUnit = nbkField.unit
                val mdUnit = UnitMapper.getMasterDataUnit(nbkUnit, nbkField.type)
                requireNotNull(mdUnit) { "Field ${nbkField.name} has no MD unit for NBK unit $nbkUnit" }
                nbkField.value?.let { v ->
                    NumericValueDto(
                        value = v as Double,
                        numerator = UnitMeasurementDto(SimpleKeyDto(mdUnit.unitKey)),
                    )
                }
            }
            // TODO non-numeric fields need a lovTypeKey? Maybe we can take this also from MaterialDetailBuilder
            // Do we even have non numeric fields to migrate?
        }
    }

    fun createMachine(
        nbkMachine: MdMigrationMasterDataDetailDto,
        masterDataType: MachineMasterDataType,
    ): Pair<HeaderDto, List<DetailDto>>? {
        println("migrating machine ${nbkMachine.key} of type ${nbkMachine.type}")

        // Get basic machine fields
        val designationField = findFieldByName(nbkMachine, "designation")
        val displayDesignationField = findFieldByName(nbkMachine, "displayDesignation")
        val manufacturerField = findFieldByName(nbkMachine, "manufacturer")
        val typeField = findFieldByName(nbkMachine, "type")
        val technicalDescriptionField = findFieldByName(nbkMachine, "technicalDescription")

        // Get currency fields
        val masterdataBaseCurrencyField = findFieldByName(nbkMachine, "masterdataBaseCurrency")
            ?: FieldParameter(
                name = "masterdataBaseCurrency",
                value = "EUR",
                type = "Currency",
                unit = null,
                source = "M"
            ).also { println("No masterdataBaseCurrency for machine ${nbkMachine.key} of type ${nbkMachine.type}") }

        val baseCurrencyField = findFieldByName(nbkMachine, "baseCurrency")
            ?: masterdataBaseCurrencyField.also { println("No baseCurrency for machine ${nbkMachine.key} of type ${nbkMachine.type}") }

        // Get investment fields
        val investBaseField = findFieldByName(nbkMachine, "investBase")
        val investFundamentField = findFieldByName(nbkMachine, "investFundament")
        val investMiscField = findFieldByName(nbkMachine, "investMisc")
        val investSetupField = findFieldByName(nbkMachine, "investSetup")
        // TODO residual values field(s)

        val classificationFields = listOfNotNull(
            manufacturerField,
            typeField,
            technicalDescriptionField,
            findFieldByName(nbkMachine, "connectedLoad"),
            findFieldByName(nbkMachine, "powerOnTimeRate"),
            findFieldByName(nbkMachine, "gasConsumptionMeltingPerHour"),
            findFieldByName(nbkMachine, "gasConsumptionKeepWarmPerHour"),
            findFieldByName(nbkMachine, "oxigenConsumptionPerHour"),
            findFieldByName(nbkMachine, "castExcipientsConsumptionPerHour"),
            findFieldByName(nbkMachine, "machineWeight"),
            findFieldByName(nbkMachine, "requiredSpaceGross"),
            findFieldByName(nbkMachine, "requiredSpaceNet"),
            findFieldByName(nbkMachine, "maintenanceRate"),
            findFieldByName(nbkMachine, "consumableRate"),
            findFieldByName(nbkMachine, "depreciationTime"),
        )

        val header = createHeader(
            masterDataType,
            nbkMachine.key,
            nbkMachine.type,
            displayDesignationField,
            designationField,
            baseCurrencyField,
            classificationFields,
        )

        val details = createInvestmentAndEmissionDetails(
            headerKey = (header?.key as SimpleKeyDto?)?.key ?: "unknown",
            investBaseField = investBaseField,
            investFundamentField = investFundamentField,
            investMiscField = investMiscField,
            investSetupField = investSetupField,
            // emissionField = emissionField, // TODO: Add when emission mapping is implemented
            currency = masterdataBaseCurrencyField,
            representsAccountData = nbkMachine.representsAccountData,
        )

        return if (header == null) null else header to details
    }

    const val MACHINE_CLASSIFICATION_TYPE_ENERGY_SOURCE = "tset.ref.classification-type.energy-source"
    const val MACHINE_CLASSIFICATION_TYPE_MACHINE_CLASS = "tset.ref.classification-type.machine"
    const val MACHINE_CLASSIFICATION_TYPE_CONSUMPTION = "tset.ref.classification-type.consumption"

    private fun createHeader(
        masterDataType: MachineMasterDataType,
        originalKey: String,
        originalType: String?,
        displayDesignationField: FieldParameter?,
        designationField: FieldParameter?,
        baseCurrencyField: FieldParameter?,
        fields: List<FieldParameter>,
    ): HeaderDto? {
        val key = originalType?.let { "$originalKey-$originalType" } ?: originalKey

        // Determine energy source classification based on machine data
        val energySourceClassifications = determineEnergySourceClassification(
            findFieldByName(fields, "connectedLoad"),
            findFieldByName(fields, "gasConsumptionMeltingPerHour"),
            findFieldByName(fields, "gasConsumptionKeepWarmPerHour")
        )

        val classifications = mutableMapOf<SimpleKeyDto, List<SimpleKeyDto>>()

        // Add energy source classification
        classifications[SimpleKeyDto(MACHINE_CLASSIFICATION_TYPE_ENERGY_SOURCE)] = energySourceClassifications.map { SimpleKeyDto(it) }

        // Determine machine class classification based on machine type and characteristics
        val machineClassClassification = determineMachineClassClassification(fields)
        classifications[SimpleKeyDto(MACHINE_CLASSIFICATION_TYPE_MACHINE_CLASS)] = listOf(SimpleKeyDto(machineClassClassification))

        // Determine consumption classification based on machine consumption data
        val consumptionClassifications = determineConsumptionClassifications(fields)
        if (consumptionClassifications.isNotEmpty()) {
            classifications[SimpleKeyDto(MACHINE_CLASSIFICATION_TYPE_CONSUMPTION)] = consumptionClassifications.map { SimpleKeyDto(it) }
        }

        // Create classification field values based on selected classification types
        val classificationFieldValues = createClassificationFieldValues(
            fields,
            // key,
            // masterDataType,
            energySourceClassifications,
            machineClassClassification,
            consumptionClassifications,
        )

        val currencyIsoCode = baseCurrencyField?.value?.toString()
        if (currencyIsoCode == null) {
            println("Cannot create machine $key because no currency is available.")
            // TODO is this a good enough reason to return early?
            return null
        }

        val detailValueSchema = createMachineDetailValueSchema()

        return HeaderDto(
            key = SimpleKeyDto(key),
            name = displayDesignationField?.value?.toString() ?: designationField?.value?.toString() ?: key,
            headerTypeKey = SimpleKeyDto(MACHINE_HEADER_TYPE),
            active = true,
            detailValueSchema = detailValueSchema,
            classifications = classifications.toMap(),
            classificationFieldValues = classificationFieldValues,
        )
    }

    private fun createMachineDetailValueSchema(): ValueTypeDetailValueSchemaDto {
        return ValueTypeDetailValueSchemaDto(
            detailValueTypeMapping = mapOf(
                SimpleKeyDto(DETAIL_VALUE_INVEST_BASE_KEY) to
                    DetailValueTypeDto(
                        key = SimpleKeyDto(DETAIL_VALUE_INVEST_BASE_KEY),
                        name = "Base investment",
                        index = 0,
                        detailValueSchema = NumericFieldSchemaDto(
                            unitOfMeasurement = UnitOfMeasurementTypeDto(
                                numerator = AnyCurrencyTypeDto(SimpleKeyDto("EUR")),
                                denominator = UnitTypeDto(SimpleKeyDto("tset.unit.piece.piece")),
                            ),
                        ),
                    ),
                SimpleKeyDto(DETAIL_VALUE_INVEST_FUNDAMENT_KEY) to
                    DetailValueTypeDto(
                        key = SimpleKeyDto(DETAIL_VALUE_INVEST_FUNDAMENT_KEY),
                        name = "Foundation investment",
                        index = 1,
                        detailValueSchema = NumericFieldSchemaDto(
                            unitOfMeasurement = UnitOfMeasurementTypeDto(
                                numerator = AnyCurrencyTypeDto(SimpleKeyDto("EUR")),
                                denominator = UnitTypeDto(SimpleKeyDto("tset.unit.piece.piece")),
                            ),
                        ),
                    ),
                SimpleKeyDto(DETAIL_VALUE_INVEST_MISC_KEY) to
                    DetailValueTypeDto(
                        key = SimpleKeyDto(DETAIL_VALUE_INVEST_MISC_KEY),
                        name = "Other investment",
                        index = 2,
                        detailValueSchema = NumericFieldSchemaDto(
                            unitOfMeasurement = UnitOfMeasurementTypeDto(
                                numerator = AnyCurrencyTypeDto(SimpleKeyDto("EUR")),
                                denominator = UnitTypeDto(SimpleKeyDto("tset.unit.piece.piece")),
                            ),
                        ),
                    ),
                SimpleKeyDto(DETAIL_VALUE_INVEST_SETUP_KEY) to
                    DetailValueTypeDto(
                        key = SimpleKeyDto(DETAIL_VALUE_INVEST_SETUP_KEY),
                        name = "Setup investment",
                        index = 3,
                        detailValueSchema = NumericFieldSchemaDto(
                            unitOfMeasurement = UnitOfMeasurementTypeDto(
                                numerator = AnyCurrencyTypeDto(SimpleKeyDto("EUR")),
                                denominator = UnitTypeDto(SimpleKeyDto("tset.unit.piece.piece")),
                            ),
                        ),
                    ),
                SimpleKeyDto(DETAIL_VALUE_EMISSION_KEY) to
                    DetailValueTypeDto(
                        key = SimpleKeyDto(DETAIL_VALUE_EMISSION_KEY),
                        name = "Emission",
                        index = 4,
                        detailValueSchema = NumericFieldSchemaDto(
                            unitOfMeasurement = UnitOfMeasurementTypeDto(
                                numerator = UnitTypeDto(SimpleKeyDto("tset.unit.emission.kilogram_co2e")),
                                denominator = UnitTypeDto(SimpleKeyDto("tset.unit.piece.piece")),
                            ),
                        ),
                    ),
            ),
        )
    }

    private fun determineEnergySourceClassification(
        connectedLoadField: FieldParameter?,
        gasConsumptionMeltingField: FieldParameter?,
        gasConsumptionKeepWarmField: FieldParameter?,
    ): List<String> {
        val energySources = mutableListOf<String>()

        val hasGasConsumption = (gasConsumptionMeltingField?.value as? Number)?.toDouble()?.let { it > 0 } == true ||
                               (gasConsumptionKeepWarmField?.value as? Number)?.toDouble()?.let { it > 0 } == true

        val hasElectricalLoad = (connectedLoadField?.value as? Number)?.toDouble()?.let { it > 0 } == true

        // TODO is it possible for a machine to have both gas and electricity as energy source?
        // Or are they mutually exclusive?

        if (hasGasConsumption) {
            energySources.add("tset.ref.classification.energy-source.gas")
        }

        if (hasElectricalLoad) {
            energySources.add("tset.ref.classification.energy-source.electricity")
        }

        // TODO is it legal to have no energy sources? Do we need to fall back to electricity then?

        return energySources
    }

    private fun createClassificationFieldValues(
        fields: List<FieldParameter>,
        // key: String,
        // masterDataType: MachineMasterDataType,
        energySourceClassifications: List<String>,
        machineClassClassification: String?,
        consumptionClassifications: List<String>,
    ): Map<@Valid SimpleKeyDto, @Valid FieldValueDto> {
        val fieldValues = mutableMapOf<SimpleKeyDto, FieldValueDto>()

        // TODO is this the proper way of handling units? Is there anything still missing in that regard?

        // Add energy source specific fields based on classification
        energySourceClassifications.forEach { energySource ->
            when (energySource) {
                "tset.ref.classification.energy-source.electricity" -> {
                    // Add electricity-specific fields using unit conversion
                    findFieldByName(fields, "powerOnTimeRate")?.let { field ->
                        createFieldValueWithUnitConversion(field)?.let { value ->
                            fieldValues[SimpleKeyDto("tset.ref.field.machine.electricityPowerOnTimeRate")] = value
                        }
                    }

                    // Add connected load field for electricity using unit conversion
                    findFieldByName(fields, "connectedLoad")?.let { field ->
                        createFieldValueWithUnitConversion(field)?.let { value ->
                            fieldValues[SimpleKeyDto("tset.ref.field.machine.electricityConnectedLoad")] = value
                        }
                    }
                }
                "tset.ref.classification.energy-source.gas" -> {
                    // Add gas power on time rate using unit conversion (Rate type -> percentage)
                    findFieldByName(fields, "powerOnTimeRate")?.let { field ->
                        createFieldValueWithUnitConversion(field)?.let { value ->
                            fieldValues[SimpleKeyDto("tset.ref.field.machine.gasPowerOnTimeRate")] = value
                        }
                    }

                    // TODO just keeping this comment here for documenting my thought process,
                    // should not be totally relevant anymore.
                    // The SQL mentions a unit by its id. It appears to map to the tset.unit.power.kilowatt unit,
                    // so the mapping here is correct. Though I'm not sure if it needs the id or the key...
                    // The connectedLoad field in NBK has @DefaultUnit(DefaultUnit.KILOWATT) - but can it also be Watt?
                    // If so, we will need to check the unit and convert if necessary.

                    findFieldByName(fields, "connectedLoad")?.let { field ->
                        createFieldValueWithUnitConversion(field)?.let { value ->
                            fieldValues[SimpleKeyDto("tset.ref.field.machine.gasConnectedLoad")] = value
                        }
                    }

                    findFieldByName(fields, "gasConsumptionMeltingPerHour")?.let { field ->
                        createFieldValueWithUnitConversion(field)?.let { value ->
                            fieldValues[SimpleKeyDto("tset.ref.field.machine.gasConsumptionMeltingPerHour")] = value
                        }
                    }

                    // Add gas consumption keep warm per hour (convert from cubic cm to cubic meters)
                    findFieldByName(fields, "gasConsumptionKeepWarmPerHour")?.let { field ->
                        createFieldValueWithUnitConversion(field)?.let { value ->
                            fieldValues[SimpleKeyDto("tset.ref.field.machine.gasConsumptionKeepWarmPerHour")] = value
                        }
                    }
                }

                else -> {
                    println("Unsupported energy source: $energySource")
                }
            }
        }

        // Add machine class specific fields (same for all machine classes)
        // TODO we probably always have a machine class, so we should not check for null?
        machineClassClassification?.let { machineClass ->
            // Add depreciation time (convert from seconds to years)
            findFieldByName(fields, "depreciationTime")?.value?.let { value ->
                fieldValues[SimpleKeyDto("tset.ref.field.machine.depreciationTime")] =
                    // TODO this converts from seconds to years, is this correct?
                    createNumericFieldValue(value, "tset.unit.time.year") { it / (365.25 * 24 * 3600) }
            }

            // Add maintenance rate using unit conversion (Rate type -> percentage)
            findFieldByName(fields, "maintenanceRate")?.let { field ->
                createFieldValueWithUnitConversion(field)?.let { value ->
                    fieldValues[SimpleKeyDto("tset.ref.field.machine.maintenanceRate")] = value
                }
            }

            // TODO does this mapping make any sense?
            // Add operational supply rate (map from consumableRate) using unit conversion
            findFieldByName(fields, "consumableRate")?.let { field ->
                createFieldValueWithUnitConversion(field)?.let { value ->
                    fieldValues[SimpleKeyDto("tset.ref.field.machine.operationalSupplyRate")] = value
                }
            }

            // Add required space gross using unit conversion
            findFieldByName(fields, "requiredSpaceGross")?.let { field ->
                createFieldValueWithUnitConversion(field)?.let { value ->
                    fieldValues[SimpleKeyDto("tset.ref.field.machine.requiredSpaceGross")] = value
                }
            }

            // Add required space net using unit conversion
            findFieldByName(fields, "requiredSpaceNet")?.let { field ->
                createFieldValueWithUnitConversion(field)?.let { value ->
                    fieldValues[SimpleKeyDto("tset.ref.field.machine.requiredSpaceNet")] = value
                }
            }

            // Add machine weight using unit conversion
            findFieldByName(fields, "machineWeight")?.let { field ->
                createFieldValueWithUnitConversion(field)?.let { value ->
                    fieldValues[SimpleKeyDto("tset.ref.field.machine.machineWeight")] = value
                }
            }
        }

        // Add consumption classification specific fields
        consumptionClassifications.forEach { consumptionClassification ->
            when (consumptionClassification) {
                "tset.ref.classification.consumption.oxygen" -> {
                    // Add oxygen consumption per hour field using unit conversion
                    findFieldByName(fields, "oxigenConsumptionPerHour")?.let { field ->
                        createFieldValueWithUnitConversion(field)?.let { value ->
                            fieldValues[SimpleKeyDto("tset.ref.field.machine.oxigenConsumptionPerHour")] = value
                        }
                    }
                }
                "tset.ref.classification.consumption.castExcipients" -> {
                    // Add cast excipients consumption per hour field using unit conversion
                    findFieldByName(fields, "castExcipientsConsumptionPerHour")?.let { field ->
                        createFieldValueWithUnitConversion(field)?.let { value ->
                            fieldValues[SimpleKeyDto("tset.ref.field.machine.castExcipientsConsumptionPerHour")] = value
                        }
                    }
                }
                else -> {
                    println("Unsupported consumption classification: $consumptionClassification")
                }
            }
        }

        return fieldValues
    }

    private fun determineMachineClassClassification(fields: List<FieldParameter>): String {
        val typeField = findFieldByName(fields, "type")?.value?.toString() ?: ""
        return typeField
        // TODO Map NBK machine type to MD machine classification based on the SQL definitions
        // It should just be a simple 1-to-1 mapping.
    }

    private fun determineConsumptionClassifications(fields: List<FieldParameter>): List<String> {
        val consumptionClassifications = mutableListOf<String>()

        // Check for oxygen consumption
        findFieldByName(fields, "oxigenConsumptionPerHour")?.value?.let { value ->
            val consumptionValue = (value as? Number)?.toDouble() ?: 0.0
            if (consumptionValue > 0) {
                consumptionClassifications.add("tset.ref.classification.consumption.oxygen")
            }
        }

        // Check for cast excipients consumption
        findFieldByName(fields, "castExcipientsConsumptionPerHour")?.value?.let { value ->
            val consumptionValue = (value as? Number)?.toDouble() ?: 0.0
            if (consumptionValue > 0) {
                consumptionClassifications.add("tset.ref.classification.consumption.castExcipients")
            }
        }

        return consumptionClassifications
    }

    private fun createInvestmentAndEmissionDetails(
        headerKey: String,
        investBaseField: FieldParameter?,
        investFundamentField: FieldParameter?,
        investMiscField: FieldParameter?,
        investSetupField: FieldParameter?,
        // TODO residual values detail
        currency: FieldParameter,
        representsAccountData: Boolean,
    ): List<DetailDto> {
        val details = mutableListOf<DetailDto>()

        // Create investment details
        investBaseField?.let { field ->
            details.add(createInvestmentDetail(headerKey, DETAIL_VALUE_INVEST_BASE_KEY, field, currency, representsAccountData))
        }

        investFundamentField?.let { field ->
            details.add(createInvestmentDetail(headerKey, DETAIL_VALUE_INVEST_FUNDAMENT_KEY, field, currency, representsAccountData))
        }

        investMiscField?.let { field ->
            details.add(createInvestmentDetail(headerKey, DETAIL_VALUE_INVEST_MISC_KEY, field, currency, representsAccountData))
        }

        investSetupField?.let { field ->
            details.add(createInvestmentDetail(headerKey, DETAIL_VALUE_INVEST_SETUP_KEY, field, currency, representsAccountData))
        }

        // TODO: Create residual values detail

        return details
    }

    private fun createInvestmentDetail(
        headerKey: String,
        detailValueTypeKey: String,
        investmentField: FieldParameter,
        currency: FieldParameter,
        representsAccountData: Boolean,
    ): DetailDto {
        // Use unit conversion for Money fields (investment fields)
        val investmentValue = createFieldValueWithUnitConversion(investmentField)
        val finalValue = if (investmentValue is NumericValueDto) {
            investmentValue.value
        } else {
            // TODO probably unnecessary
            (investmentField.value as? Number)?.toDouble() ?: 0.0
        }

        // TODO idk if this is the correct way to get the currency...
        val currencyKey = currency.value as? String ?: "EUR"

        // Reuse MaterialEffectivityBuilder - the logic for creating effectivities is the same.
        val effectivities = MaterialEffectivityBuilder.createSharedEffectivities(representsAccountData)
        return DetailDto(
            effectivities = effectivities,
            headerKey = SimpleKeyDto(headerKey),
            value = NumericDetailValueDto(
                value = finalValue,
                numerator = CurrencyMeasurementDto(SimpleKeyDto(currencyKey)),
                denominator = UnitMeasurementDto(SimpleKeyDto("tset.unit.piece.piece")),
            ),
            active = true,
            detailValueTypeKey = SimpleKeyDto(detailValueTypeKey),
        )
    }
}
